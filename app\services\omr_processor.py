import cv2
import numpy as np
from typing import Dict, Tuple, List
import logging

logger = logging.getLogger(__name__)


class OMRProcessor:
    """X<PERSON> lý OMR (Optical Mark Recognition) cho phiếu trả lời trắc nghiệm"""

    def __init__(self):
        # C<PERSON>u hình các tham số cho việc nhận dạng
        self.bubble_threshold = 0.4  # Ngưỡng để xác định bubble được tô (40%)
        self.min_bubble_area = 50    # Diện tích tối thiểu của bubble
        self.max_bubble_area = 200   # Diện tích tối đa của bubble
        
        # ROI coordinates (tỷ lệ phần trăm dựa trên layout thực tế)
        self.roi_coords = {
            # Mã đề (top-right, 4 chữ số x 10 hàng)
            'test_code': {'x': 0.82, 'y': 0.08, 'w': 0.15, 'h': 0.12},
            # <PERSON><PERSON> b<PERSON><PERSON> danh (top-right, 8 chữ số x 10 hàng) 
            'student_id': {'x': 0.55, 'y': 0.08, 'w': 0.25, 'h': 0.12},
            # Phần I - 40 câu trắc nghiệm (4 cột x 10 hàng)
            'answers_part1': {'x': 0.05, 'y': 0.28, 'w': 0.90, 'h': 0.35}
        }

    async def process_image(self, image_content: bytes) -> Tuple[Dict, Dict[int, str]]:
        """
        Xử lý ảnh phiếu trả lời và trích xuất thông tin
        """
        try:
            # Chuyển đổi bytes thành image
            nparr = np.frombuffer(image_content, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if image is None:
                raise Exception("Cannot decode image")

            # Tiền xử lý ảnh
            processed_image = self.preprocess_image(image)

            # Căn chỉnh ảnh (perspective correction)
            aligned_image = self.align_image(processed_image)

            # Trích xuất mã đề
            test_code = self.extract_test_code(aligned_image)
            
            # Trích xuất số báo danh
            student_id = self.extract_student_id(aligned_image)
            
            # Trích xuất 40 câu trả lời phần I
            answers = self.extract_part1_answers(aligned_image)

            student_info = {
                "test_code": test_code,
                "student_id": student_id,
                "name": "",
                "class": ""
            }

            return student_info, answers

        except Exception as e:
            logger.error(f"Error processing image: {str(e)}")
            raise Exception(f"Image processing failed: {str(e)}")

    def preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """Tiền xử lý ảnh với GaussianBlur và adaptiveThreshold"""
        # Resize nếu ảnh quá lớn
        height, width = image.shape[:2]
        if width > 1200:
            scale = 1200 / width
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height))

        # Chuyển sang grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Gaussian blur để khử nhiễu
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # Adaptive threshold để xử lý lighting không đều
        binary = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        return binary

    def align_image(self, image: np.ndarray) -> np.ndarray:
        """Căn chỉnh ảnh dựa vào các marker đen ở góc"""
        # Tìm các hình vuông đen (markers) ở góc
        contours, _ = cv2.findContours(255 - image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Lọc các marker (hình vuông đen lớn)
        markers = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 800:  # Marker phải đủ lớn
                # Xấp xỉ contour thành đa giác
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)

                if len(approx) == 4:  # Hình chữ nhật
                    x, y, w, h = cv2.boundingRect(contour)
                    # Kiểm tra tỷ lệ gần vuông
                    if 0.8 < w/h < 1.2:
                        markers.append((x + w//2, y + h//2))

        # Nếu tìm thấy đủ 4 markers, thực hiện perspective correction
        if len(markers) >= 4:
            markers = sorted(markers, key=lambda p: (p[1], p[0]))
            
            # Sắp xếp thành TL, TR, BL, BR
            top_markers = sorted(markers[:2], key=lambda p: p[0])
            bottom_markers = sorted(markers[-2:], key=lambda p: p[0])
            
            src_points = np.float32([
                top_markers[0],    # TL
                top_markers[1],    # TR  
                bottom_markers[0], # BL
                bottom_markers[1]  # BR
            ])
            
            # Tính toán kích thước đích
            width = int(max(
                np.linalg.norm(src_points[1] - src_points[0]),
                np.linalg.norm(src_points[3] - src_points[2])
            ))
            height = int(max(
                np.linalg.norm(src_points[2] - src_points[0]),
                np.linalg.norm(src_points[3] - src_points[1])
            ))
            
            dst_points = np.float32([
                [0, 0], [width, 0], [0, height], [width, height]
            ])
            
            # Thực hiện perspective transform
            matrix = cv2.getPerspectiveTransform(src_points, dst_points)
            aligned = cv2.warpPerspective(image, matrix, (width, height))
            return aligned

        return image

    def get_roi_region(self, image: np.ndarray, roi_name: str) -> np.ndarray:
        """Lấy vùng ROI từ ảnh"""
        height, width = image.shape
        roi = self.roi_coords[roi_name]
        
        x1 = int(width * roi['x'])
        y1 = int(height * roi['y'])
        x2 = int(width * (roi['x'] + roi['w']))
        y2 = int(height * (roi['y'] + roi['h']))
        
        return image[y1:y2, x1:x2]

    def extract_test_code(self, image: np.ndarray) -> str:
        """Trích xuất mã đề từ vùng bubble mã đề (4 chữ số)"""
        region = self.get_roi_region(image, 'test_code')
        return self.extract_digit_sequence(region, num_digits=4, default="0000")

    def extract_student_id(self, image: np.ndarray) -> str:
        """Trích xuất số báo danh từ vùng bubble SBD (8 chữ số)"""
        region = self.get_roi_region(image, 'student_id')
        return self.extract_digit_sequence(region, num_digits=8, default="00000000")

    def extract_digit_sequence(self, region: np.ndarray, num_digits: int, default: str) -> str:
        """Trích xuất chuỗi số từ vùng bubble (num_digits cột x 10 hàng)"""
        if region.size == 0:
            return default
            
        height, width = region.shape
        col_width = width // num_digits
        
        result = ""
        for col in range(num_digits):
            # Lấy vùng của cột hiện tại
            col_region = region[:, col * col_width:(col + 1) * col_width]
            
            # Tìm digit được chọn trong cột này (0-9)
            digit = self.find_selected_digit_in_column(col_region)
            result += str(digit) if digit != -1 else "0"
        
        return result if result else default

    def find_selected_digit_in_column(self, col_region: np.ndarray) -> int:
        """Tìm digit được chọn trong một cột (10 hàng cho 0-9)"""
        height, width = col_region.shape
        row_height = height // 10
        
        for digit in range(10):
            # Lấy vùng của digit hiện tại
            y1 = digit * row_height
            y2 = (digit + 1) * row_height
            digit_region = col_region[y1:y2, :]
            
            # Kiểm tra xem có được tô không
            if self.is_region_filled(digit_region):
                return digit
        
        return -1  # Không tìm thấy digit nào được chọn

    def extract_part1_answers(self, image: np.ndarray) -> Dict[int, str]:
        """Trích xuất 40 câu trả lời phần I (4 cột x 10 hàng)"""
        region = self.get_roi_region(image, 'answers_part1')
        
        if region.size == 0:
            return {}
            
        answers = {}
        height, width = region.shape
        col_width = width // 4  # 4 cột
        
        for col in range(4):
            # Lấy vùng của cột hiện tại
            col_region = region[:, col * col_width:(col + 1) * col_width]
            row_height = height // 10  # 10 hàng
            
            for row in range(10):
                question_num = col * 10 + row + 1  # Câu 1-40
                
                # Lấy vùng của câu hỏi hiện tại
                y1 = row * row_height
                y2 = (row + 1) * row_height
                question_region = col_region[y1:y2, :]
                
                # Tìm đáp án được chọn (A, B, C, D)
                answer = self.find_selected_answer(question_region)
                answers[question_num] = answer
        
        return answers

    def find_selected_answer(self, question_region: np.ndarray) -> str:
        """Tìm đáp án được chọn trong một câu hỏi (A, B, C, D)"""
        height, width = question_region.shape
        option_width = width // 4  # 4 lựa chọn A, B, C, D
        
        options = ['A', 'B', 'C', 'D']
        
        for i, option in enumerate(options):
            # Lấy vùng của lựa chọn hiện tại
            option_region = question_region[:, i * option_width:(i + 1) * option_width]
            
            # Kiểm tra xem có được tô không
            if self.is_region_filled(option_region):
                return option
        
        return ""  # Không có đáp án được chọn

    def is_region_filled(self, region: np.ndarray) -> bool:
        """Kiểm tra vùng có được tô không (>40% pixel đen)"""
        if region.size == 0:
            return False
            
        # Tính tỷ lệ pixel đen
        total_pixels = region.size
        black_pixels = np.sum(region == 0)  # Pixel đen có giá trị 0
        fill_ratio = black_pixels / total_pixels
        
        return fill_ratio > self.bubble_threshold
