#!/usr/bin/env python3
"""
Script test đơn gi<PERSON>n cho hệ thống OMR
"""

import asyncio
import sys
import os

# Thêm thư mục gốc vào Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.omr_processor import OMRProcessor
from app.services.excel_processor import ExcelProcessor
import logging

# Cấu hình logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_omr_processor():
    """Test OMR processor với dữ liệu giả"""
    logger.info("🧪 Testing OMR Processor...")

    try:
        processor = OMRProcessor()

        # Test với ảnh giả (tạo ảnh đen trắng đơn giản)
        import numpy as np
        import cv2

        # Tạo ảnh test 800x600 pixels
        test_image = np.ones((600, 800, 3), dtype=np.uint8) * 255  # Ảnh trắng

        # Thêm một số marker đen ở góc
        test_image[10:30, 10:30] = 0  # Top-left
        test_image[10:30, 770:790] = 0  # Top-right
        test_image[570:590, 10:30] = 0  # Bottom-left
        test_image[570:590, 770:790] = 0  # Bottom-right

        # Encode thành bytes
        _, encoded_image = cv2.imencode(".jpg", test_image)
        image_bytes = encoded_image.tobytes()

        # Test xử lý ảnh
        student_info, answers = await processor.process_image(image_bytes)

        logger.info(f"✅ Student Info: {student_info}")
        logger.info(f"✅ Answers extracted: {len(answers)} questions")

        # Kiểm tra cấu trúc kết quả
        assert isinstance(student_info, dict)
        assert "test_code" in student_info
        assert "student_id" in student_info
        assert isinstance(answers, dict)

        logger.info("✅ OMR Processor test passed!")
        return True

    except Exception as e:
        logger.error(f"❌ OMR Processor test failed: {str(e)}")
        return False


async def test_excel_processor():
    """Test Excel processor với dữ liệu giả"""
    logger.info("🧪 Testing Excel Processor...")

    try:
        processor = ExcelProcessor()

        # Tạo file Excel test
        import pandas as pd
        import io

        # Tạo dữ liệu test theo format mới
        test_data = {
            "Mã Đề": ["0001", "0002", "0003"],
            "Câu hỏi 1": ["A", "B", "C"],
            "Câu hỏi 2": ["B", "C", "A"],
            "Câu hỏi 3": ["C", "A", "B"],
            "Câu hỏi 4": ["D", "D", "D"],
            "Câu hỏi 5": ["A", "B", "C"],
        }

        df = pd.DataFrame(test_data)

        # Tạo file Excel trong memory
        excel_buffer = io.BytesIO()
        df.to_excel(excel_buffer, index=False)
        excel_buffer.seek(0)

        # Tạo mock UploadFile
        class MockUploadFile:
            def __init__(self, content):
                self.content = content
                self.filename = "test.xlsx"

            async def read(self):
                return self.content

        mock_file = MockUploadFile(excel_buffer.getvalue())

        # Test đọc đáp án
        answer_keys = await processor.load_answer_keys(mock_file)

        logger.info(f"✅ Loaded answer keys for {len(answer_keys)} test codes")

        # Kiểm tra cấu trúc kết quả
        assert isinstance(answer_keys, dict)
        assert "1" in answer_keys  # Test code được lưu dưới dạng string
        assert len(answer_keys["1"]) == 5  # 5 câu hỏi
        assert answer_keys["1"][1] == "A"  # Câu 1 đáp án A

        logger.info("✅ Excel Processor test passed!")
        return True

    except Exception as e:
        logger.error(f"❌ Excel Processor test failed: {str(e)}")
        return False


async def test_integration():
    """Test tích hợp toàn bộ hệ thống"""
    logger.info("🧪 Testing System Integration...")

    try:
        from app.services.grading_service import calculate_score

        # Test dữ liệu
        student_answers = {1: "A", 2: "B", 3: "C", 4: "D", 5: ""}  # Câu 5 bỏ trống
        correct_answers = {1: "A", 2: "C", 3: "C", 4: "D", 5: "A"}

        # Test tính điểm
        score, detailed_results = calculate_score(student_answers, correct_answers)

        logger.info(f"✅ Score: {score}/10")
        logger.info(f"✅ Correct: {detailed_results['correct_count']}")
        logger.info(f"✅ Incorrect: {detailed_results['incorrect_count']}")
        logger.info(f"✅ Blank: {detailed_results['blank_count']}")

        # Kiểm tra kết quả
        assert score == 6.0  # 3/5 * 10 = 6.0
        assert detailed_results["correct_count"] == 3
        assert detailed_results["incorrect_count"] == 1
        assert detailed_results["blank_count"] == 1

        logger.info("✅ Integration test passed!")
        return True

    except Exception as e:
        logger.error(f"❌ Integration test failed: {str(e)}")
        return False


async def main():
    """Chạy tất cả các test"""
    logger.info("🚀 Starting OMR System Tests...")

    tests = [
        ("OMR Processor", test_omr_processor),
        ("Excel Processor", test_excel_processor),
        ("System Integration", test_integration),
    ]

    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running {test_name} Test")
        logger.info(f"{'='*50}")

        result = await test_func()
        results.append((test_name, result))

    # Tổng kết
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")

    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1

    logger.info(f"\nTotal: {passed}/{len(results)} tests passed")

    if passed == len(results):
        logger.info("🎉 All tests passed! OMR system is ready!")
    else:
        logger.error("⚠️ Some tests failed. Please check the issues above.")


if __name__ == "__main__":
    asyncio.run(main())
