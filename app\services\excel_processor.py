import pandas as pd
from fastapi import UploadFile
from typing import Dict
import io
import logging

logger = logging.getLogger(__name__)


class ExcelProcessor:
    """Xử lý file Excel chứa đáp án"""

    async def load_answer_keys(
        self, excel_file: UploadFile
    ) -> Dict[str, Dict[int, str]]:
        """
        Đọc đáp án từ file Excel
        Hỗ trợ nhiều format:
        - Format 1: Mã Đề | Câu hỏi 1 | Câu hỏi 2 | ... | Câu hỏi n
        - Format 2: TestCode | question_1 | question_2 | ... | question_n
        """
        try:
            # Đọc nội dung file
            content = await excel_file.read()

            # Đọc file Excel vào DataFrame
            df = pd.read_excel(io.BytesIO(content))

            # Chuẩn hóa tên cột (xoá BOM và khoảng trắng)
            df.columns = [str(col).strip().replace("\ufeff", "") for col in df.columns]
            logger.info(f"Excel Columns: {df.columns.tolist()}")

            # Tìm cột mã đề (hỗ trợ cả 'Mã Đề' và 'TestCode')
            test_code_col = None
            for col in df.columns:
                if col in ["Mã Đề", "TestCode", "Ma De", "Test Code"]:
                    test_code_col = col
                    break

            if test_code_col is None:
                raise ValueError(
                    "Excel file is missing required column: 'Mã Đề' or 'TestCode'"
                )

            answer_keys = {}

            for _, row in df.iterrows():
                test_code = str(row[test_code_col]).strip()
                answers = {}

                # Duyệt qua các cột để tìm câu hỏi
                for col in df.columns:
                    question_num = None

                    # Format 1: "Câu hỏi 1", "Câu hỏi 2", ...
                    if col.startswith("Câu hỏi"):
                        try:
                            question_num = int(col.split()[-1])  # Lấy số cuối cùng
                        except (ValueError, IndexError):
                            continue

                    # Format 2: "question_1", "question_2", ...
                    elif col.startswith("question_"):
                        try:
                            question_num = int(col.split("_")[1])
                        except (ValueError, IndexError):
                            continue

                    # Format 3: Cột số trực tiếp "1", "2", "3", ...
                    elif col.isdigit():
                        try:
                            question_num = int(col)
                        except ValueError:
                            continue

                    # Nếu tìm thấy câu hỏi, lấy đáp án
                    if question_num is not None:
                        try:
                            answer = str(row[col]).strip().upper()
                            if answer in ["A", "B", "C", "D"]:
                                answers[question_num] = answer
                        except (ValueError, AttributeError):
                            continue  # Bỏ qua nếu giá trị không hợp lệ

                answer_keys[test_code] = answers
                logger.info(f"Loaded {len(answers)} answers for test code {test_code}")

            return answer_keys

        except Exception as e:
            logger.error(f"❌ Error loading answer keys: {str(e)}")
            raise Exception(f"Failed to load answer keys from Excel file: {str(e)}")
